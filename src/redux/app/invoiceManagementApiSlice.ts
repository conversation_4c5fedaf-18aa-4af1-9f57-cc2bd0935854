import {ApiSliceIdentifier} from 'Constants/enums';
import {apiSlice} from 'redux/apiSlice';
import {ITenantBillingFilterDTO, TenantBillingsApiDTO} from './types/invoice.type';

const apiSliceIdentifier = ApiSliceIdentifier.TENANT_FACADE;

/**
 * Builds a `where` filter object for querying tenant billings based on the provided filter criteria.
 *
 * @param filter - An object containing filter criteria such as status, search value, and date range.
 * @returns A `where` object suitable for use in API queries, including conditions for invoice status,
 *          search value (applied to invoiceId and amount), and created date range.
 */
const buildWhere = (filter: TenantBillingsApiDTO) => {
  const where: Record<string, unknown> = {};

  if (filter.status?.length) {
    where.invoiceStatus = {inq: filter.status};
  }

  if (filter.billingCustomerId) {
    where.billingCustomerId = filter.billingCustomerId;
  }

  if (filter.tenantId) {
    where.tenantId = filter.tenantId;
  }

  if (filter.searchValue?.trim()) {
    const search = filter.searchValue.trim();
    where.or = [
      {invoiceId: {ilike: `%${search}%`}},
      {tenantName: {ilike: `%${search}%`}},
      {amount: {ilike: `%${search}%`}},
    ];
  }

  if (filter.dateRange) {
    where.createdOn = {
      between: [filter.dateRange.startDate.toISOString(), filter.dateRange.endDate.toISOString()],
    };
  }

  return where;
};

/**
 * Composes an invoice filter object for API requests based on the provided filter query.
 *
 * @param filterQuery - The filter criteria as a `TenantBillingsApiDTO` object.
 * @returns An `ITenantBillingFilterDTO` object containing the filter parameters (`limit`, `offset`, `order`, and `where`).
 *          Returns an empty object if the filter query is empty or undefined.
 */
const composeInvoiceFilter = (filterQuery: TenantBillingsApiDTO): ITenantBillingFilterDTO => {
  if (!filterQuery || Object.keys(filterQuery).length === 0) return {};

  const {limit, offset, order} = filterQuery;
  const where = buildWhere(filterQuery);

  return {
    ...(limit && {limit}),
    ...(offset && {offset}),
    ...(order && {order}),
    ...(Object.keys(where).length && {where}),
  };
};

/**
 * Injects invoice management related endpoints into the base API slice.
 *
 * @remarks
 * This slice provides endpoints for fetching tenant billings, counting tenant billings,
 * and retrieving all invoice statuses.
 *
 * @example
 * // Usage with RTK Query hooks
 * const { data, error } = useGetTenantBillingsQuery(filterQuery);
 *
 * @see composeInvoiceFilter
 *
 * @endpoint getTenantBillings - Fetches tenant billing records based on filter criteria.
 * @endpoint getTenantBillingsCount - Retrieves the count of tenant billings matching the filter.
 * @endpoint getInvoiceStatus - Gets all possible invoice statuses.
 */
export const invoiceApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getTenantBillings: builder.query({
      query: (filterQuery: TenantBillingsApiDTO) => {
        const filter = composeInvoiceFilter(filterQuery);
        return {
          url: '/tenant-billings',
          method: 'GET',
          params: {
            filter: JSON.stringify(filter),
          },
          apiSliceIdentifier,
        };
      },
    }),
    getTenantBillingsCount: builder.query({
      query: (filterQuery = {}) => {
        const filter = composeInvoiceFilter(filterQuery);
        return {
          url: '/tenant-billings/count',
          method: 'GET',
          params: filter?.where && {
            where: JSON.stringify({
              ...filter.where,
            }),
          },
          apiSliceIdentifier,
        };
      },
    }),
    getInvoiceStatus: builder.query({
      query: () => ({
        url: '/tenant-billings/all-status',
        method: 'GET',
        apiSliceIdentifier,
      }),
    }),
    getInvoices: builder.query({
      query: (filterQuery: TenantBillingsApiDTO) => {
        const filter = composeInvoiceFilter(filterQuery);
        return {
          url: '/invoices',
          method: 'GET',
          params: {
            filter: JSON.stringify(filter),
          },
          apiSliceIdentifier,
        };
      },
    }),
    getInvoicesCount: builder.query({
      query: (filterQuery = {}) => {
        const filter = composeInvoiceFilter(filterQuery);
        return {
          url: '/invoices/count',
          method: 'GET',
          params: filter?.where && {
            where: JSON.stringify({
              ...filter.where,
            }),
          },
          apiSliceIdentifier,
        };
      },
    }),
    getInvoiceById: builder.query({
      query: (invoiceId: string) => ({
        url: `/invoices/${invoiceId}`,
        method: 'GET',
        apiSliceIdentifier,
      }),
    }),
    downloadInvoiceById: builder.query({
      query: ({invoiceId, stripeInvoiceId}: {invoiceId: string; stripeInvoiceId: string}) => ({
        url: `/invoices/${invoiceId}/pdf-url`,
        method: 'GET',
        params: {
          stripeInvoiceId,
        },
        apiSliceIdentifier,
      }),
    }),
  }),
});

export const {
  useGetTenantBillingsQuery,
  useGetTenantBillingsCountQuery,
  useGetInvoiceStatusQuery,
  useGetInvoicesQuery,
  useGetInvoicesCountQuery,
  useGetInvoiceByIdQuery,
  useDownloadInvoiceByIdQuery,
} = invoiceApiSlice;
