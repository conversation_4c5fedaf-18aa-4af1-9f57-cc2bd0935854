import {ApiSliceIdentifier} from 'Constants/enums';
import {apiSlice} from 'redux/apiSlice';
import {PlanApiDTO, PlanFilterDTO, PlanType} from './types/plan.type';

const apiSliceIdentifier = ApiSliceIdentifier.TENANT_FACADE;

const buildWhere = (filter: PlanApiDTO) => {
  const where: Record<string, unknown> = {};

  if (filter.status?.length) {
    where.status = {inq: filter.status};
  }

  if (filter.billingCycleId?.length) {
    where.billingCycleId = {inq: filter.billingCycleId};
  }

  (['configureDeviceId', 'tier'] as const).forEach(key => {
    if (filter[key]) where[key] = filter[key];
  });

  if (filter.searchValue?.trim()) {
    const search = filter.searchValue.trim();
    // OR condition for name or price
    where.or = [{name: {ilike: `%${search}%`}}, {price: {ilike: `%${search}%`}}];
  }

  return where;
};

const composePlanFilter = (filterQuery: PlanApiDTO): PlanFilterDTO => {
  if (!filterQuery || Object.keys(filterQuery).length === 0) return {};

  const {limit, offset, order} = filterQuery;
  const where = buildWhere(filterQuery);

  return {
    ...(limit && {limit}),
    ...(offset && {offset}),
    ...(order && {order}),
    ...(Object.keys(where).length && {where}),
    include: [
      {relation: 'billingCycle'},
      {relation: 'currency'},
      {relation: 'planSize'},
      {relation: 'planHistories'},
      {relation: 'configureDevice'},
    ],
  };
};

export const planApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getPlans: builder.query({
      query: (filterQuery: PlanApiDTO) => {
        const filter = composePlanFilter(filterQuery);
        return {
          url: '/plans',
          method: 'GET',
          params: {
            filter: JSON.stringify(filter),
          },
          apiSliceIdentifier,
        };
      },
    }),
    getPlansCount: builder.query({
      query: (filterQuery = {}) => {
        const filter = composePlanFilter(filterQuery);
        return {
          url: '/plans/count',
          method: 'GET',
          params: filter?.where && {
            where: JSON.stringify({
              ...filter.where,
            }),
          },
          apiSliceIdentifier,
        };
      },
    }),
    createPlan: builder.mutation({
      query: (planData: PlanType) => {
        return {
          url: '/plans',
          method: 'POST',
          body: planData,
          apiSliceIdentifier,
        };
      },
    }),
    updateStatus: builder.mutation({
      query: ({planId, status}: {planId: string; status: number}) => {
        return {
          url: `/plans/${planId}/status`,
          method: 'PATCH',
          body: {status},
          apiSliceIdentifier,
        };
      },
    }),
    getDevices: builder.query({
      query: (filterQuery = {}) => ({
        url: '/configure-devices',
        method: 'GET',
        apiSliceIdentifier,
        params: {
          filter: JSON.stringify(filterQuery),
        },
      }),
    }),
    getStatus: builder.query({
      query: () => ({
        url: '/plans/all-status',
        method: 'GET',
        apiSliceIdentifier,
      }),
    }),
    getTenures: builder.query({
      query: filter => ({
        url: '/subscriptions-tenure',
        method: 'GET',
        params: filter ? {filter: JSON.stringify(filter)} : {},
        apiSliceIdentifier,
      }),
    }),
    getCurrencies: builder.query({
      query: () => ({
        url: '/currencies',
        method: 'GET',
        apiSliceIdentifier,
      }),
    }),
  }),
});

export const {
  useGetPlansQuery,
  useGetPlansCountQuery,
  useLazyGetPlansCountQuery,
  useLazyGetPlansQuery,
  useCreatePlanMutation,
  useGetDevicesQuery,
  useGetTenuresQuery,
  useGetCurrenciesQuery,
  useLazyGetDevicesQuery,
  useLazyGetTenuresQuery,
  useLazyGetCurrenciesQuery,
  useUpdateStatusMutation,
  useGetStatusQuery,
} = planApiSlice;
