import {Typography, useTheme} from '@mui/material';
import Box from '@mui/material/Box';
import {usePermissions} from 'Components/PermissionRedirectWrapper/PermissionProvider';
import {getIndicatorColor} from 'Components/StatusChip/statusChip.util';
import PermissionKey from 'Constants/enums/permissions';
import {Integers} from 'Helpers/integers';
import {getColorFromString, getNameComponents} from 'Helpers/utils';
import CustomPieChart, {IPieData} from 'Pages/Dashboard/CustomPieChart/CustomPieChart';
import {RouteNames} from 'Routes/routeNames';
import {capitalize} from 'lodash';
import {useCallback} from 'react';
import {useNavigate} from 'react-router';
import {useGetTenantStatusMetricsQuery} from 'redux/app/tenantManagementApiSlice';
import {TenantStatus} from 'redux/app/types/tenant.type';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import {AnyObject} from 'yup';
import DashboardTable from './Dashboard/DashboardTable';
import TenantOverview from './Dashboard/TenantOverview';
import {headerTypographyProps} from './Dashboard/styles';
import {getTitleForTenantId} from './Tenants/TenantUtils';
import {tenantStatusMapToStatusChip} from './Tenants/tenants.utils';

type StatusMetricsType = Record<string, {count: number; status: string}>;

const Home = () => {
  const {data, error: statusMetricsError} = useGetTenantStatusMetricsQuery({
    limit: 6,
    order: 'createdOn DESC',
  });
  const {status: statusMetrics, tenants} = data ?? {};
  const theme = useTheme();

  const pieChartData =
    Object.keys((statusMetrics ?? {}) as StatusMetricsType).reduce<IPieData[]>((acc, key) => {
      const status = tenantStatusMapToStatusChip[Number(key) as TenantStatus];
      const obj: {count: number; status: string} = statusMetrics
        ? (statusMetrics as StatusMetricsType)[key]
        : {count: 0, status: ''};
      const pushObj = {
        name: getTitleForTenantId(key) ?? '',
        value: obj?.count ?? 0,
        color: getColorFromString(theme, getIndicatorColor(status)),
        tag: {
          key: key,
        },
      };
      acc.push(pushObj);
      return acc;
    }, []) || [];
  const {data: currentUser, isLoading: isLoadingCurrentUser} = useGetUserQuery();
  const navigate = useNavigate();
  const {hasPermission} = usePermissions();

  const handlePieChartClick = useCallback(
    (data: AnyObject) => {
      const status = data?.tag?.key;
      if (!status && !hasPermission(PermissionKey.ViewTenant)) return;
      navigate(RouteNames.TENANTS, {
        state: {statusFilter: new Set([status])},
      });
    },
    [navigate],
  );

  const drawChart = () => {
    return (
      <Box sx={{display: 'flex', flexDirection: 'column', alignItems: 'start'}}>
        <Box sx={{...headerTypographyProps}}>Tenants by Provisioning</Box>
        <Box sx={{border: theme => `1px solid ${theme.palette.body[Integers.OneHundred]}`, borderRadius: '0.625rem'}}>
          <CustomPieChart data={pieChartData} width={390} height={407} onClick={handlePieChartClick} />
        </Box>
      </Box>
    );
  };

  const buildHi = () => {
    const name = capitalize(getNameComponents(currentUser?.firstName)?.first);
    return (
      <Box width={'100%'} sx={{mb: 1}}>
        <Typography component={'span'} sx={{fontSize: '1.25rem', fontWeight: 700, color: 'body.dark'}}>
          Hi {isLoadingCurrentUser ? '...' : name + '!👋'}
        </Typography>
      </Box>
    );
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
      }}
      data-testid="HomePage"
    >
      {buildHi()}
      <TenantOverview statusMetrics={statusMetrics} error={statusMetricsError} />
      <Box sx={{height: '1.5rem'}} />
      <Box
        sx={{
          display: 'flex',
          flexDirection: {xs: 'column', sm: 'row'},
          alignItems: 'flex-start',
          gap: 1,
        }}
      >
        {drawChart()}
        <DashboardTable tenants={tenants} />
      </Box>
    </Box>
  );
};

export default Home;
