import {Box, Button, CircularProgress, styled, Typography} from '@mui/material';
import ActivateConfirmationIcon from 'Assets/confirmation-icons/ActivateConfirmIcon';
import DeactivateConfirmIcon from 'Assets/confirmation-icons/DeactivateConfirmIcon';
import {DefaultDialog} from 'Components/DefaultDialog/DefaultDialog';
import EllipsisText from 'Components/EllipsisText/EllipsisText';
import {Integers} from 'Helpers/integers';

/**
 * Props for the user activation/deactivation confirmation dialog.
 */
interface UserActivateDeactivateDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  actionType: 'Activate' | 'Deactivate';
  userName: string;
  email?: string;
  roleName?: string;
  isLoading: boolean;
}

const IconWrapper = styled(Box)(() => ({
  display: 'flex',
  justifyContent: 'center',
}));

interface KeyIconContainerProps {
  $active: boolean;
}

const KeyIconContainer = styled(Box, {
  shouldForwardProp: prop => prop !== '$active',
})<KeyIconContainerProps>(({theme, $active}) => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  width: '5rem',
  height: '5rem',
  borderRadius: '50%',
  border: `1px solid ${theme.palette.white[Integers.TwoHundred]}`,
  padding: '1rem',
  backgroundColor: $active ? theme.palette.alert.success.bg : theme.palette.alert.error.bg,
  fill: 'transparent',
  color: $active ? theme.palette.alert.success.bg : theme.palette.alert.error.bg,
}));

/**
 * User activation/deactivation confirmation dialog.
 * @param param0 - Props for the dialog.
 * @returns The rendered dialog component.
 */
export default function UserActivateDeactivateDialog({
  open,
  onClose,
  onConfirm,
  actionType,
  userName,
  email,
  roleName,
  isLoading,
}: Readonly<UserActivateDeactivateDialogProps>) {
  const icon =
    actionType === 'Activate' ? (
      <ActivateConfirmationIcon sx={{fill: 'transparent'}} />
    ) : (
      <DeactivateConfirmIcon sx={{fill: 'transparent'}} />
    );
  const actionMessage =
    actionType === 'Activate'
      ? 'Are you sure you want to activate this user?'
      : 'Are you sure you want to deactivate this user?';

  return (
    <DefaultDialog title={actionType + ' User'} maxWidth={400} open={open} onClose={onClose}>
      <Box sx={{display: 'flex', flexDirection: 'column', alignItems: 'center'}}>
        <IconWrapper>
          <KeyIconContainer $active={actionType === 'Activate'}>{icon}</KeyIconContainer>
        </IconWrapper>
        <Typography sx={{mt: 2, fontSize: '1.125rem', fontWeight: 700, color: 'body.dark', textAlign: 'center'}}>
          {actionMessage}
        </Typography>
        <Box
          sx={{
            mt: 2,
            width: '100%',
            border: '1px solid',
            borderColor: 'body.100',
            borderRadius: '0.25rem',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            p: 2,
          }}
        >
          <Typography
            component="span"
            sx={{
              fontSize: '1rem',
              fontWeight: 700,
              color: 'body.900',
              textAlign: 'center',
              m: 0,
            }}
          >
            {userName}
          </Typography>

          <EllipsisText
            text={email ?? ''}
            sx={{
              fontSize: '0.875rem',
              color: 'body.500',
              fontWeight: 500,
              textAlign: 'center',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 1,
              WebkitBoxOrient: 'vertical',
              wordBreak: 'break-word',
            }}
          />

          <Typography
            component="span"
            sx={{
              fontSize: '0.75rem',
              fontWeight: 700,
              backgroundColor: 'secondary.50',
              borderWidth: '0.0625rem',
              borderStyle: 'solid',
              borderColor: 'secondary.50',
              padding: '3px 10px',
              borderRadius: '4px',
              color: 'secondary.700',
              textAlign: 'center',
              m: 0,
            }}
          >
            {roleName}
          </Typography>
        </Box>
        <Box
          sx={{
            mt: 4,
            display: 'flex',
            gap: 1,
            flexDirection: 'row',
            width: '100%',
            fontSize: '1rem',
            fontWeight: 600,
          }}
        >
          <Button onClick={onClose} variant="outlined" color="inherit" disabled={isLoading} sx={{flex: 1}}>
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            variant="contained"
            color={actionType === 'Activate' ? 'primary' : 'error'}
            disabled={isLoading}
            sx={{
              borderColor: 'body.100',
              borderRadius: '0.375rem',
              color: isLoading ? 'primary' : 'other.white',
              fontWeight: 'inherit',
              fontSize: 'inherit',
              backgroundColor: 'secondary.main',
              flex: 1,
            }}
            startIcon={isLoading ? <CircularProgress size={18} color="inherit" /> : null}
            data-testid={actionType === 'Activate' ? 'dialog-activate-button' : 'dialog-deactivate-button'}
          >
            {actionType}
          </Button>
        </Box>
      </Box>
    </DefaultDialog>
  );
}
