import {MoreHoriz} from '@mui/icons-material';

import {Box, Divider, Grid, ListItemText, Menu, MenuItem, Typography} from '@mui/material';
import EditIcon from 'Assets/EditIcon';
import BackdropLoader from 'Components/BackdropLoader';
import BorderButton from 'Components/BorderButton/BorderButton';
import Breadcrumb from 'Components/Breadcrumb/Breadcrumb';
import PermissionsUI from 'Components/PermissionView/PermissionView';
import PermissionWrapper from 'Components/PermissionWrapper';
import {TableCellBoxWithToolTip} from 'Components/TableCellBox';
import UserStatusChip from 'Components/UserStatusChip/UserStatusChip';
import {defaultDateFormat} from 'Constants/enums';
import PermissionKey from 'Constants/enums/permissions';
import {Integers} from 'Helpers/integers';
import {getFullName} from 'Helpers/utils';
import {useEffect, useState} from 'react';
import {useNavigate} from 'react-router';
import {RouteNames} from 'Routes/routeNames';
import {detailPageSubtitleLabelSx, detailPageTitleLabelSx, gridSize} from 'styles/pages/Common.styles';
import {useUserManagementDialogs} from '../hooks/useUserManagementDialogs';
import UserActivateDeactivateDialog from '../UserActivateDeactivateDialog/UserActivateDeactivateDialog';
import {UserStatus} from '../userManagement.utils';
import UserResendActivationLinkDialog from '../UserResendActivationLinkDialog/UserResendActivationLinkDialog';
import useUserDetailPageState from './hooks/useUserDetailPageState';
import {useGetUserDetailLocationMetaData} from './userDetail.util';

const PX = 1.5;
enum UserAction {
  DEACTIVATE = 'Deactivate',
  ACTIVATE = 'Activate',
  RESEND_INVITATION = 'Resend Invitation',
}

/**
 * UserDetails component displays detailed information about a specific user,
 * including their role, email address, creation and modification dates, and permissions.
 * It also provides actions such as editing the user and deactivating the user via a menu.
 *
 * @returns {JSX.Element} The rendered user detail page.
 *
 * @remarks
 * - Uses hooks to fetch user details and related metadata.
 * - Shows a loading indicator while user data is being fetched.
 * - Includes breadcrumbs for navigation context.
 * - Displays user status, role, email, created/modified dates, and creator information.
 * - Provides permission details and a close button.
 * - Includes a menu for additional user actions (e.g., deactivate).
 */

export default function UserDetails() {
  const {user: passedUser} = useGetUserDetailLocationMetaData();
  const {user, isUserLoading, userError, role, createdByUser} = useUserDetailPageState(
    passedUser?.userTenantId ?? '',
    passedUser?.roleId,
  );

  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const [menuTitle, setMenuTitle] = useState<UserAction>(UserAction.DEACTIVATE);

  // Dialog/action logic
  const {
    dialogOpen,
    dialogAction,
    dialogUser,
    handleDialogClose,
    handleDialogConfirm,
    resendDialogOpen,
    resendDialogUser,
    handleResendDialogClose,
    handleResendDialogConfirm,
    handleActionClick,
    isPatching,
    isResending,
  } = useUserManagementDialogs({
    refetchUsers: () => {
      navigate(-1);
    },
  });

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDeactivateUser = () => {
    // Add deactivate user logic here
    if (!user) return;
    switch (user.status) {
      case UserStatus.INACTIVE:
        handleActionClick('activate', user);
        break;
      case UserStatus.ACTIVE:
        handleActionClick('deactivate', user);
        break;
      default:
        handleActionClick('send-link', user);
        break;
    }
    handleMenuClose();
  };

  const breadcrumbs = () => {
    return [
      {label: 'User Management', url: RouteNames.USER_MANAGEMENT},
      {
        label: getFullName(user) ?? 'User information',
        url: RouteNames.VIEW_USER,
      },
    ];
  };

  useEffect(() => {
    if (!user) {
      return;
    }
    switch (user?.status) {
      case UserStatus.INACTIVE:
        setMenuTitle(UserAction.ACTIVATE);
        break;
      case UserStatus.ACTIVE:
        setMenuTitle(UserAction.DEACTIVATE);
        break;
      default:
        setMenuTitle(UserAction.RESEND_INVITATION);
        break;
    }
  }, [user]);

  const buildCreatedBy = () => {
    const renderingCondition = createdByUser && !userError && !isUserLoading;
    return (
      renderingCondition && (
        <Grid size={gridSize}>
          <Typography sx={detailPageTitleLabelSx}>Created by</Typography>
          <TableCellBoxWithToolTip sx={detailPageSubtitleLabelSx}>{getFullName(createdByUser)}</TableCellBoxWithToolTip>
        </Grid>
      )
    );
  };

  const buildBottomSection = () => {
    return (
      <Box sx={{display: 'flex', flexDirection: 'column', mt: 'auto', mb: 1.5}}>
        <Divider sx={{my: 2}} />
        <Box sx={{alignSelf: 'flex-end'}} px={PX}>
          <BorderButton
            sx={{height: '3.125rem', minWidth: '6.25rem', fontSize: '1rem', fontWeight: 700}}
            fullWidth={false}
            data-testid="close-button"
            onClick={() => navigate(-1)}
          >
            Close
          </BorderButton>
        </Box>
      </Box>
    );
  };

  const buildPermissionSection = () => {
    const hasPermissionArr = role?.permissions && role.permissions.length > 0;
    return (
      hasPermissionArr && (
        <Box sx={{mx: PX}}>
          <Divider sx={{my: 3}} />
          <PermissionsUI permissions={role?.permissions ?? []} />
        </Box>
      )
    );
  };

  const buildTopPart = () => {
    if (!user) return null;

    return (
      <Box display="flex" alignItems="center" gap={1}>
        <Box display={'flex'} flexDirection={'column'} gap={1}>
          <Box display={'flex'} flexDirection={'row'} alignItems="center" gap={1}>
            <Typography sx={{fontSize: '1.25rem', fontWeight: 700}}>{getFullName(user)}</Typography>
            <UserStatusChip user={user} />
          </Box>
          <Breadcrumb items={breadcrumbs()} separator="|" />
        </Box>
        <Box sx={{ml: 'auto', display: 'flex', flexDirection: 'row', gap: 1}}>
          <PermissionWrapper permission={PermissionKey.UpdateTenantUser}>
            <BorderButton
              sx={{
                fontWeight: 700,
                px: '1.56rem',
                height: '2.5rem',
                display: user.status === UserStatus.INACTIVE ? 'none' : 'flex',
              }}
              onClick={() =>
                navigate(RouteNames.EDIT_USER, {
                  state: {user},
                })
              }
              data-testid="edit-user-button"
            >
              <Box sx={{display: 'flex', alignItems: 'center', gap: 0.5}}>
                <EditIcon sx={{fill: 'white', width: '1.2rem'}} />
                Edit
              </Box>
            </BorderButton>
          </PermissionWrapper>

          {/* add menu button having deactivate user button in it  */}

          <BorderButton
            sx={{px: 0, minWidth: 0, width: '2.5rem', height: '2.5rem'}}
            onClick={handleMenuOpen}
            data-testid="more-options-button"
          >
            <MoreHoriz sx={{fill: theme => theme.palette.body[Integers.FiveHundred]}} />
          </BorderButton>
        </Box>
      </Box>
    );
  };

  useEffect(() => {
    if (!passedUser) {
      navigate(RouteNames.USER_MANAGEMENT, {replace: true});
    }
  }, [passedUser]);

  if (isUserLoading) {
    return <BackdropLoader />;
  }

  if (!user) {
    return null;
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 0.5,
      }}
    >
      {buildTopPart()}
      {/* Header */}

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 1.5,
        }}
      >
        <Box
          sx={{
            border: '1px solid',
            borderColor: 'body.200',
            borderRadius: '0.375rem',
            pt: 1,
            minHeight: '80vh',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <Typography sx={{fontSize: '1rem', fontWeight: 700, color: 'body.900'}} px={PX}>
            User information
          </Typography>

          <Grid container spacing={2} px={PX} mt={1}>
            <Grid size={gridSize}>
              <Typography sx={detailPageTitleLabelSx}>Role</Typography>
              <TableCellBoxWithToolTip sx={detailPageSubtitleLabelSx}>{user.roleName}</TableCellBoxWithToolTip>
            </Grid>

            <Grid size={gridSize}>
              <Typography sx={detailPageTitleLabelSx}>Email address</Typography>
              <TableCellBoxWithToolTip sx={{...detailPageSubtitleLabelSx, textTransform: 'lowercase'}}>
                {user.email}
              </TableCellBoxWithToolTip>
            </Grid>

            <Grid size={gridSize}>
              <Typography sx={detailPageTitleLabelSx}>Created on</Typography>
              <Typography sx={detailPageSubtitleLabelSx}>{defaultDateFormat(user.createdOn ?? '')}</Typography>
            </Grid>

            <Grid size={gridSize}>
              <Typography sx={detailPageTitleLabelSx}>Modified on</Typography>
              <Typography sx={detailPageSubtitleLabelSx}>{defaultDateFormat(user.modifiedOn ?? '')}</Typography>
            </Grid>

            {buildCreatedBy()}
          </Grid>
          {buildPermissionSection()}

          {buildBottomSection()}
        </Box>
        {/* Loading overlay */}

        {(isPatching || isResending) && <BackdropLoader />}

        <UserActivateDeactivateDialog
          open={dialogOpen}
          onClose={handleDialogClose}
          onConfirm={handleDialogConfirm}
          actionType={dialogAction ?? 'Activate'}
          userName={dialogUser?.firstName ?? ''}
          email={dialogUser?.email ?? ''}
          roleName={dialogUser?.roleName ?? ''}
          isLoading={isPatching}
        />
        {/* Resend Activation Link Confirmation Dialog */}
        <UserResendActivationLinkDialog
          open={resendDialogOpen}
          onClose={handleResendDialogClose}
          onConfirm={handleResendDialogConfirm}
          userName={resendDialogUser?.firstName ?? ''}
          email={resendDialogUser?.email ?? ''}
          isLoading={isResending}
        />
        <Menu
          id="user-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={handleMenuClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          data-testid="user-action-menu"
          container={document.body}
          disablePortal={true}
          keepMounted={true}
        >
          <MenuItem onClick={handleDeactivateUser} data-testid="user-action-menuitem">
            <ListItemText>{menuTitle}</ListItemText>
          </MenuItem>
        </Menu>
      </Box>
    </Box>
  );
}
