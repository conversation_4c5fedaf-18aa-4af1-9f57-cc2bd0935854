// @vitest-environment jsdom
import {ThemeProvider, createTheme} from '@mui/material/styles';
import '@testing-library/jest-dom';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import React from 'react';
import {MemoryRouter} from 'react-router-dom';
import {LeadStatus} from 'redux/app/types/lead.type';
import {vi} from 'vitest';
import PendingTenantDetail from './PendingTenantDetail';

// Theme mock
const robustTheme = createTheme({
  palette: {
    secondary: {main: '#dc004e', 100: '#f8f8f8', 300: '#e0e0e0'},
    white: {main: '#fff', 300: '#e0e0e0'},
    black: {main: '#000000'},
    body: {
      300: '#bdbdbd',
      500: '#757575',
      800: '#424242',
      900: '#212121',
      100: '#f5f5f5',
      200: '#eeeeee',
      dark: '#000000',
    },
    alert: {
      warning: {bg: '#fff3cd', main: '#856404', onBg: '#856404', border: '#ffeaa7'},
      success: {bg: '#d4edda', main: '#155724', onBg: '#155724', border: '#c3e6cb'},
      error: {bg: '#f8d7da', main: '#721c24', onBg: '#721c24', border: '#f5c6cb'},
    },
  },
});

vi.mock('@mui/material/styles', async importOriginal => {
  const actual = await importOriginal();
  return Object.assign({}, actual, {
    useTheme: () => robustTheme,
  });
});

function renderWithTheme(ui: React.ReactElement, {route = '/pending-tenants/123'} = {}) {
  return render(
    <MemoryRouter initialEntries={[route]}>
      <ThemeProvider theme={robustTheme}>{ui}</ThemeProvider>
    </MemoryRouter>,
  );
}

// Mocks
const mockEnqueueSnackbar = vi.fn();
const mockNavigate = vi.fn();
const mockUseParams = vi.fn();

vi.mock('notistack', () => ({
  useSnackbar: () => ({
    enqueueSnackbar: mockEnqueueSnackbar,
  }),
}));

vi.mock('react-router', () => ({
  useNavigate: () => mockNavigate,
  useParams: () => mockUseParams(),
}));

// Mock API
const mockUseGetLeadByIdQuery = vi.fn();
const mockUpdateStatus = vi.fn();
vi.mock('redux/app/leadManagementApiSlice', () => ({
  useGetLeadByIdQuery: () => mockUseGetLeadByIdQuery(),
  useUpdateLeadByIdMutation: () => [mockUpdateStatus, {isFetching: false}],
}));

// Mock utility functions
vi.mock('./pendingTenants.utils', () => ({
  formatUSPhoneNumber: vi.fn((countryCode: string, phoneNumber: string) => `${countryCode} ${phoneNumber}`),
  getFontColor: vi.fn(() => '#000000'),
  getIndicatorColor: vi.fn(() => '#ff0000'),
  getStatusColor: vi.fn(() => '#fff3cd'),
  getStatusLabel: vi.fn((status: number) => {
    const labels: Record<number, string> = {
      0: 'Pending',
      1: 'Converted',
      2: 'Invalid',
    };
    return labels[status] || 'Unknown';
  }),
  LeadStatus: {
    PENDING: 0,
    CONVERTED: 1,
    INVALID: 2,
  },
}));

// Mock date formatting to match the actual implementation
vi.mock('Constants/enums', () => ({
  defaultDateFormat: vi.fn((date: string) => {
    if (!date) return '';
    // Mock the actual dayjs format: 'DD MMM, YYYY'
    const d = new Date(date);
    return d
      .toLocaleDateString('en-GB', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
      })
      .replace(/\s/g, ' '); // Ensure consistent spacing
  }),
}));

// Sample lead data
const mockLeadData = {
  id: '123',
  firstName: 'John',
  lastName: 'Doe',
  companyName: 'Test Company',
  email: '<EMAIL>',
  designation: 'CEO',
  phoneNumber: '*********0',
  countryCode: '+1',
  status: LeadStatus.PENDING,
  address: {
    id: 'addr-123',
    city: 'New York',
    state: 'NY',
    street: '123 Main St',
    zipCode: '10001',
    country: 'USA',
  },
  addressId: 'addr-123',
  isValidated: false,
  deleted: false,
  deletedOn: '',
  deletedBy: '',
  createdOn: '2024-01-15T10:30:00Z',
  modifiedOn: '2024-01-15T10:30:00Z',
  createdBy: 'admin',
  modifiedBy: 'admin',
};

describe('PendingTenantDetail', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockUpdateStatus.mockReset();
    mockUpdateStatus.mockResolvedValue({error: undefined});
    // Default setup
    mockUseParams.mockReturnValue({leadId: '123'});
  });

  it('renders loading state when data is loading', () => {
    mockUseGetLeadByIdQuery.mockReturnValue({
      data: undefined,
      isFetching: true,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);

    // BackdropLoader renders a CircularProgress with testid="circularProgress"
    expect(screen.getByTestId('circularProgress')).toBeInTheDocument();
  });

  it('renders error message when leadId is missing', () => {
    mockUseParams.mockReturnValue({leadId: undefined});

    mockUseGetLeadByIdQuery.mockReturnValue({
      data: undefined,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);

    expect(
      screen.getByText('Unable to load pending tenant page: Missing pending tenant information.'),
    ).toBeInTheDocument();
  });

  it('renders lead details correctly when data is loaded', async () => {
    mockUseGetLeadByIdQuery.mockReturnValue({
      data: mockLeadData,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);

    // Check company name and status (use getAllByText for multiple occurrences)
    expect(screen.getAllByText('Test Company')).toHaveLength(2); // Header + breadcrumb
    expect(screen.getByText('Pending')).toBeInTheDocument();

    // Check breadcrumb
    expect(screen.getByText('Pending Tenants')).toBeInTheDocument();

    // Check action buttons (use getAllByTestId since there are multiple elements with same testid)
    expect(screen.getAllByTestId('convert-button')).toHaveLength(2); // Button + inner div
    expect(screen.getAllByTestId('invalid-button')).toHaveLength(2); // Button + inner div
    expect(screen.getByText('Convert To Tenant')).toBeInTheDocument();
    expect(screen.getByText('Mark As Invalid')).toBeInTheDocument();

    // Check lead information section
    expect(screen.getByText('Pending tenant information')).toBeInTheDocument();

    // Check personal details
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('CEO')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('*************')).toBeInTheDocument();

    // Check address details
    expect(screen.getByText('New York')).toBeInTheDocument();
    expect(screen.getByText('NY')).toBeInTheDocument();

    // Check created date (format: DD MMM YYYY - without comma)
    expect(screen.getByText('15 Jan 2024')).toBeInTheDocument();

    // Check close button
    expect(screen.getByText('Close')).toBeInTheDocument();
  });

  it('handles missing optional fields gracefully', () => {
    const leadWithMissingFields = {
      ...mockLeadData,
      designation: undefined,
      phoneNumber: undefined,
      address: {
        ...mockLeadData.address,
        city: undefined,
        state: undefined,
      },
    };

    mockUseGetLeadByIdQuery.mockReturnValue({
      data: leadWithMissingFields,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);

    // Should show dash for missing designation
    expect(screen.getByText('-')).toBeInTheDocument();

    // Should still render the component without crashing (use getAllByText for multiple occurrences)
    expect(screen.getAllByText('Test Company')).toHaveLength(2);
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('displays error snackbar when API call fails', async () => {
    const mockError = {status: 404, message: 'Lead not found'};

    mockUseGetLeadByIdQuery.mockReturnValue({
      data: undefined,
      isFetching: false,
      error: mockError,
    });

    renderWithTheme(<PendingTenantDetail />);

    await waitFor(() => {
      expect(mockEnqueueSnackbar).toHaveBeenCalledWith('Failed to pending tenant data', {variant: 'error'});
    });
  });

  it('navigates back when close button is clicked', () => {
    mockUseGetLeadByIdQuery.mockReturnValue({
      data: mockLeadData,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);

    const closeButton = screen.getByText('Close');
    fireEvent.click(closeButton);

    expect(mockNavigate).toHaveBeenCalledWith(-1);
  });

  it('renders different status colors and labels correctly', () => {
    const convertedLead = {
      ...mockLeadData,
      status: LeadStatus.CONVERTED,
    };

    mockUseGetLeadByIdQuery.mockReturnValue({
      data: convertedLead,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);

    expect(screen.getByText('Converted')).toBeInTheDocument();
  });

  it('renders all grid sections with proper test ids', () => {
    mockUseGetLeadByIdQuery.mockReturnValue({
      data: mockLeadData,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);

    expect(screen.getByTestId('top-part')).toBeInTheDocument();
    expect(screen.getByTestId('lead-detail')).toBeInTheDocument();
    expect(screen.getByTestId('bottom-section')).toBeInTheDocument();
  });

  it('skips API call when leadId is not provided', () => {
    mockUseParams.mockReturnValue({leadId: undefined});

    mockUseGetLeadByIdQuery.mockReturnValue({
      data: undefined,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);

    // Just verify the component renders the error message
    expect(
      screen.getByText('Unable to load pending tenant page: Missing pending tenant information.'),
    ).toBeInTheDocument();
  });

  it('renders with valid leadId and makes API call', () => {
    mockUseParams.mockReturnValue({leadId: '123'});

    mockUseGetLeadByIdQuery.mockReturnValue({
      data: mockLeadData,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);

    // Just verify the component renders correctly with data
    expect(screen.getAllByText('Test Company')).toHaveLength(2);
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('handles empty address object gracefully', () => {
    const leadWithEmptyAddress = {
      ...mockLeadData,
      address: {},
    };

    mockUseGetLeadByIdQuery.mockReturnValue({
      data: leadWithEmptyAddress,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);

    // Should still render without crashing
    expect(screen.getAllByText('Test Company')).toHaveLength(2);
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('handles null address gracefully', () => {
    const leadWithNullAddress = {
      ...mockLeadData,
      address: null,
    };

    mockUseGetLeadByIdQuery.mockReturnValue({
      data: leadWithNullAddress,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);

    // Should still render without crashing
    expect(screen.getAllByText('Test Company')).toHaveLength(2);
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('formats phone number correctly when both country code and phone number are provided', () => {
    mockUseGetLeadByIdQuery.mockReturnValue({
      data: mockLeadData,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);

    // Check that the formatted phone number is displayed
    expect(screen.getByText('*************')).toBeInTheDocument();
  });

  it('handles missing phone number gracefully', () => {
    const leadWithoutPhone = {
      ...mockLeadData,
      phoneNumber: undefined,
      countryCode: undefined,
    };

    mockUseGetLeadByIdQuery.mockReturnValue({
      data: leadWithoutPhone,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);

    // Should still render without crashing
    expect(screen.getAllByText('Test Company')).toHaveLength(2);

    // Should display the formatted result (empty string + undefined = " undefined")
    // The actual text in the DOM shows " undefined" (with a space)
    expect(
      screen.getByText(content => {
        return content.includes('undefined');
      }),
    ).toBeInTheDocument();
  });

  it('renders breadcrumb with correct company name', () => {
    mockUseGetLeadByIdQuery.mockReturnValue({
      data: mockLeadData,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);

    // Check that breadcrumb contains the company name (appears twice)
    expect(screen.getAllByText('Test Company')).toHaveLength(2);
    expect(screen.getByText('Pending Tenants')).toBeInTheDocument();
  });

  it('renders breadcrumb with empty company name when not provided', () => {
    const leadWithoutCompanyName = {
      ...mockLeadData,
      companyName: '',
    };

    mockUseGetLeadByIdQuery.mockReturnValue({
      data: leadWithoutCompanyName,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);

    // Should still render breadcrumb
    expect(screen.getByText('Pending Tenants')).toBeInTheDocument();
    // Should render John Doe since company name is empty
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });
  // Test: Clicking convert-button opens InvalidDialog with correct props
  it('opens InvalidDialog with convert action when convert-button is clicked', async () => {
    mockUseGetLeadByIdQuery.mockReturnValue({
      data: mockLeadData,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);
    const convertBtn = screen.getAllByTestId('convert-button')[0];
    fireEvent.click(convertBtn);

    // Dialog should appear
    await waitFor(() => {
      expect(screen.getByText(/Test Company - John Doe/)).toBeInTheDocument();
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    // Should show correct dialog button
    expect(screen.getByTestId('dialog-invalid-button')).toBeInTheDocument();
  });

  // E2E: Cancel dialog for convert and invalid actions
  it('allows user to cancel InvalidDialog for both convert and invalid actions', async () => {
    mockUseGetLeadByIdQuery.mockReturnValue({
      data: mockLeadData,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);
    // Convert
    fireEvent.click(screen.getAllByTestId('convert-button')[0]);
    await waitFor(() => expect(screen.getByRole('dialog')).toBeInTheDocument());
    fireEvent.click(screen.getByText('Cancel'));
    await waitFor(() => expect(screen.queryByRole('dialog')).not.toBeInTheDocument());

    // Invalid
    fireEvent.click(screen.getAllByTestId('invalid-button')[0]);
    await waitFor(() => expect(screen.getByRole('dialog')).toBeInTheDocument());
    fireEvent.click(screen.getByText('Cancel'));
    await waitFor(() => expect(screen.queryByRole('dialog')).not.toBeInTheDocument());
  });

  // E2E: Confirm convert action
  it('handles confirm convert action in InvalidDialog', async () => {
    mockUseGetLeadByIdQuery.mockReturnValue({
      data: mockLeadData,
      isFetching: false,
      error: null,
    });

    // Mock navigation for convert
    mockNavigate.mockClear();

    renderWithTheme(<PendingTenantDetail />);
    fireEvent.click(screen.getAllByTestId('convert-button')[0]);
    await waitFor(() => expect(screen.getByRole('dialog')).toBeInTheDocument());

    // Confirm convert
    fireEvent.click(screen.getByTestId('dialog-invalid-button'));
    await waitFor(() =>
      expect(mockNavigate).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({state: expect.any(Object)}),
      ),
    );
  });

  // E2E: Confirm invalid action
  it('handles confirm invalid action in InvalidDialog', async () => {
    mockUseGetLeadByIdQuery.mockReturnValue({
      data: mockLeadData,
      isFetching: false,
      error: null,
    });

    // Mock mutation for invalid
    renderWithTheme(<PendingTenantDetail />);
    fireEvent.click(screen.getAllByTestId('invalid-button')[0]);
    await waitFor(() => expect(screen.getByRole('dialog')).toBeInTheDocument());

    // Confirm invalid
    fireEvent.click(screen.getByTestId('dialog-invalid-button'));
    await waitFor(() =>
      expect(mockUpdateStatus).toHaveBeenCalledWith({leadId: mockLeadData.id, status: LeadStatus.INVALID}),
    );
    await waitFor(() =>
      expect(mockEnqueueSnackbar).toHaveBeenCalledWith(
        'Pending tenant marked as invalid successfully!',
        expect.objectContaining({variant: 'success', subMessage: mockLeadData.companyName}),
      ),
    );
  });
  // Test: Clicking invalid-button opens InvalidDialog with correct props
  it('opens InvalidDialog with invalid action when invalid-button is clicked', async () => {
    mockUseGetLeadByIdQuery.mockReturnValue({
      data: mockLeadData,
      isFetching: false,
      error: null,
    });

    renderWithTheme(<PendingTenantDetail />);
    const invalidBtn = screen.getAllByTestId('invalid-button')[0];
    fireEvent.click(invalidBtn);

    // Dialog should appear
    await waitFor(() => {
      expect(screen.getByText(/Test Company - John Doe/)).toBeInTheDocument();
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    // Should show correct dialog button
    expect(screen.getByTestId('dialog-invalid-button')).toBeInTheDocument();
  });
});
