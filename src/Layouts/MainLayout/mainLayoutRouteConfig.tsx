import PermissionKey from 'Constants/enums/permissions';
import AddPlan from 'Pages/PlansPage/AddPlan';
import AddTenant from 'Pages/Tenants/AddTenant/AddTenantPage';
import {lazy} from 'react';
import {RouteNames} from 'Routes/routeNames';

const Home = lazy(() => import('Pages/Home'));
const TenantPage = lazy(() => import('Pages/Tenants/TenantPage'));
const TenantDetailsPage = lazy(() => import('Pages/Tenants/TenantDetails/TenantDetailsPage'));
const EditPlan = lazy(() => import('Pages/PlansPage/EditPlan'));
const EditTenant = lazy(() => import('Pages/Tenants/EditTenant/EditTenant'));

interface RouteConfig {
  path: string;
  component: React.ComponentType;
  permission?: string;
}

const mainLayoutRouteConfig: RouteConfig[] = [
  {
    path: RouteNames.HOME,
    component: Home,
  },
  {
    path: RouteNames.TENANTS,
    component: TenantPage,
    permission: PermissionKey.ViewTenant,
  },
  {
    path: RouteNames.TENANT_DETAILS,
    component: TenantDetailsPage,
    permission: PermissionKey.ViewTenant,
  },
  {
    path: RouteNames.ADD_TENANT,
    component: AddTenant,
    permission: PermissionKey.CreateTenant,
  },
  {
    path: RouteNames.ADD_PLANS,
    component: AddPlan,
    permission: PermissionKey.CreatePlan,
  },
  {
    path: RouteNames.BILLING_INVOICES,
    component: lazy(() => import('Pages/BillingInvoicePage/BillingInvoicePage')),
  },
  {
    path: RouteNames.BILLING_INVOICES_TENANT,
    component: lazy(() => import('Pages/BillingInvoicePage/InvoicePage')),
  },
  {
    path: RouteNames.INVOICE_DETAILS,
    component: lazy(() => import('Pages/BillingInvoicePage/InvoiceDetail')),
  },
  {
    path: RouteNames.PLANS,
    component: lazy(() => import('Pages/PlansPage/PlanPage')),
    permission: PermissionKey.ViewPlan,
  },
  {
    path: RouteNames.OBSERVABILITY,
    component: lazy(() => import('Pages/ObservabilityPage/ObservabilityPage')),
  },
  {
    path: RouteNames.USER_MANAGEMENT,
    component: lazy(() => import('Pages/UserManagementPage/UserManagementPage')),
    permission: PermissionKey.ViewTenantUser,
  },
  {
    path: RouteNames.ROLES_RESPONSIBILITIES,
    component: lazy(() => import('Pages/RolesResponsibilitiesPage/RolesResponsibilitiesPage')),
    permission: PermissionKey.ViewRoles,
  },
  {
    path: '/plans/:id/edit',
    component: EditPlan,
    permission: PermissionKey.UpdatePlan,
  },
  {
    path: RouteNames.ADD_USER,
    component: lazy(() => import('Pages/UserManagementPage/AddUser/AddUserPage')),
    permission: PermissionKey.CreateTenantUser,
  },
  {
    path: RouteNames.EDIT_USER,
    component: lazy(() => import('Pages/UserManagementPage/AddUser/AddUserPage')),
    permission: PermissionKey.UpdateTenantUser,
  },
  {
    path: RouteNames.VIEW_USER,
    component: lazy(() => import('Pages/UserManagementPage/UserDetail/UserDetail')),
    permission: PermissionKey.ViewTenantUser,
  },
  {
    path: RouteNames.ADD_ROLE,
    component: lazy(() => import('Pages/RolesResponsibilitiesPage/AddRole/AddRole')),
    permission: PermissionKey.AddRole,
  },

  {
    path: RouteNames.EDIT_ROLE,
    component: lazy(() => import('Pages/RolesResponsibilitiesPage/AddRole/AddRole')),
    permission: PermissionKey.UpdateRole,
  },
  {
    path: RouteNames.VIEW_ROLE,
    component: lazy(() => import('Pages/RolesResponsibilitiesPage/RoleDetail/RoleDetailView')),
    permission: PermissionKey.ViewRoles,
  },
  {
    path: '/tenants/:id/edit',
    component: EditTenant,
    permission: PermissionKey.UpdateTenant,
  },
  {
    path: RouteNames.PENDING_TENANTS,
    component: lazy(() => import('Pages/PendingTenants/PendingTenantPage')),
    permission: PermissionKey.ViewLead,
  },
  {
    path: RouteNames.ADD_PENDING_TENANT,
    component: lazy(() => import('Pages/PendingTenants/AddPendingTenant')),
    permission: PermissionKey.CreateLead,
  },
  {
    path: RouteNames.PENDING_TENANT_DETAILS,
    component: lazy(() => import('Pages/PendingTenants/PendingTenantDetail')),
    permission: PermissionKey.ViewLead,
  },
];

export default mainLayoutRouteConfig;
