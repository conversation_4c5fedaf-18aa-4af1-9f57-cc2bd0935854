import {Box, CircularProgress, SxProps} from '@mui/material';

interface CenterLoaderContainerProps {
  children: React.ReactNode;
  isLoading: boolean;
  loaderBackSx?: SxProps;
  progressSx?: SxProps;
}

/**
 * Component to display a loader in the center of a container.
 *
 * @param children - The content to be displayed.
 * @param isLoading - Whether the loader should be displayed.
 * @param loaderBackSx - Optional style overrides for the loader background.
 * @param progressSx - Optional style overrides for the progress indicator.
 * @returns The rendered component.
 */
const CenterLoaderContainer = ({children, isLoading, loaderBackSx, progressSx}: CenterLoaderContainerProps) => {
  return (
    <Box sx={{position: 'relative', minHeight: '12.6rem'}}>
      {children}
      {isLoading && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            bgcolor: 'rgba(255, 255, 255, 0.3)',
            zIndex: 1,
            ...loaderBackSx,
          }}
        >
          <CircularProgress sx={{...progressSx}} />
        </Box>
      )}
    </Box>
  );
};

CenterLoaderContainer.displayName = 'CenterLoaderContainer';

export default CenterLoaderContainer;
